#!/bin/bash

# 环境配置处理脚本
# 用于处理外部挂载的环境配置文件

ENV_FILE="/app/config/.env"
HTML_FILE="/usr/share/nginx/html/index.html"

if [ ! -f "$ENV_FILE" ]; then
    echo "❌ 环境配置文件不存在: $ENV_FILE"
    exit 0
fi

echo "🔧 处理环境配置文件: $ENV_FILE"

# 读取环境变量
while IFS= read -r line || [ -n "$line" ]; do
    # 跳过注释和空行
    if [[ $line =~ ^[[:space:]]*# ]] || [[ -z "${line// }" ]]; then
        continue
    fi
    
    # 解析变量
    if [[ $line =~ ^([^=]+)=(.*)$ ]]; then
        key="${BASH_REMATCH[1]}"
        value="${BASH_REMATCH[2]}"
        
        # 移除引号
        value=$(echo "$value" | sed 's/^["'\'']\|["'\'']$//g')
        
        echo "  设置变量: $key=$value"
        export "$key"="$value"
        
        # 特殊处理：更新nginx环境变量
        case "$key" in
            "NGINX_PORT")
                export NGINX_PORT="$value"
                ;;
            "API_PROXY_PASS"|"VITE_BACKEND_URL")
                export API_PROXY_PASS="$value"
                ;;
            "CLIENT_MAX_BODY_SIZE")
                export CLIENT_MAX_BODY_SIZE="$value"
                ;;
        esac
    fi
done < "$ENV_FILE"

# 如果HTML文件存在，进行运行时配置注入
if [ -f "$HTML_FILE" ]; then
    echo "🔄 注入运行时配置到HTML文件..."
    
    # 创建配置对象
    CONFIG_SCRIPT="<script>
    window.__APP_CONFIG__ = {
        VITE_APP_TITLE: '${VITE_APP_TITLE:-测试医院管理系统}',
        VITE_APP_ENV: '${VITE_APP_ENV:-production}',
        VITE_API_BASE_URL: '${VITE_API_BASE_URL:-/api}',
        VITE_API_TIMEOUT: '${VITE_API_TIMEOUT:-15000}',
        VITE_BACKEND_URL: '${VITE_BACKEND_URL:-http://localhost:6596}',
        VITE_APP_VERSION: '${VITE_APP_VERSION:-1.0.0}',
        VITE_YINGCHUNHUA_SDK_URL: '${VITE_YINGCHUNHUA_SDK_URL:-}',
        VITE_YINGCHUNHUA_APP_KEY: '${VITE_YINGCHUNHUA_APP_KEY:-}',
        VITE_YINGCHUNHUA_APP_SECRET_KEY: '${VITE_YINGCHUNHUA_APP_SECRET_KEY:-}'
    };
    </script>"
    
    # 在</head>标签前插入配置脚本
    sed -i "s|</head>|$CONFIG_SCRIPT\n</head>|" "$HTML_FILE"
    
    echo "✅ 运行时配置注入完成"
fi

echo "🎯 环境配置处理完成"
