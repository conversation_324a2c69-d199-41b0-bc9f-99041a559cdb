# YYHIS Web 环境配置文件
# 这是一个默认配置，请根据实际环境进行修改

# ==================== 应用基础配置 ====================
# 应用标题
VITE_APP_TITLE=测试医院管理系统

# 应用环境 (development/test/production)
VITE_APP_ENV=production

# 应用版本
VITE_APP_VERSION=1.0.0

# ==================== API配置 ====================
# API基础URL (通常使用 /api 通过nginx代理)
VITE_API_BASE_URL=/api

# API请求超时时间 (毫秒)
VITE_API_TIMEOUT=15000

# 后端服务器地址 (用于nginx代理配置)
# 注意：这个地址是容器内部访问后端的地址
VITE_BACKEND_URL=http://localhost:6596

# ==================== 第三方系统配置 ====================
# 迎春花质控系统SDK地址
VITE_YINGCHUNHUA_SDK_URL=http://**************:8094/client_app_iframe/index.js

# 迎春花系统应用密钥
VITE_YINGCHUNHUA_APP_KEY=ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09

# 迎春花系统应用秘钥
VITE_YINGCHUNHUA_APP_SECRET_KEY=YYCloud1644286723584

# ==================== Docker配置 ====================
# Nginx监听端口
NGINX_PORT=80

# API代理目标地址 (Docker环境下的后端地址)
# 如果后端在宿主机，使用: http://host.docker.internal:6596
# 如果后端在其他服务器，使用: http://backend-server-ip:port
API_PROXY_PASS=http://host.docker.internal:6596

# 客户端最大请求体大小
CLIENT_MAX_BODY_SIZE=10m
