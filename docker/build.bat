@echo off
setlocal enabledelayedexpansion

REM YYHIS Web Docker 构建脚本 (Windows版本)
REM 用于自动化构建和部署Docker镜像

REM 默认配置
set IMAGE_NAME=yyhis-web
set IMAGE_TAG=latest
set CONTAINER_NAME=yyhis-web
set HOST_PORT=8080
set CONTAINER_PORT=80

REM 颜色定义 (Windows CMD)
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set BLUE=[94m
set NC=[0m

REM 显示帮助信息
if "%1"=="help" goto :show_help
if "%1"=="-h" goto :show_help
if "%1"=="--help" goto :show_help

REM 解析命令行参数
set COMMAND=%1
if "%COMMAND%"=="" (
    echo %RED%[ERROR]%NC% 请指定命令
    goto :show_help
)

REM 检查Docker是否安装
call :check_docker
if errorlevel 1 exit /b 1

REM 执行命令
if "%COMMAND%"=="build" goto :build_image
if "%COMMAND%"=="run" goto :run_container
if "%COMMAND%"=="stop" goto :stop_container
if "%COMMAND%"=="restart" goto :restart_container
if "%COMMAND%"=="logs" goto :show_logs
if "%COMMAND%"=="clean" goto :clean_resources
if "%COMMAND%"=="deploy" goto :deploy

echo %RED%[ERROR]%NC% 未知命令: %COMMAND%
goto :show_help

:show_help
echo YYHIS Web Docker 构建脚本 (Windows版本)
echo.
echo 用法: %0 [命令] [选项]
echo.
echo 命令:
echo   build     构建Docker镜像
echo   run       运行Docker容器
echo   stop      停止Docker容器
echo   restart   重启Docker容器
echo   logs      查看容器日志
echo   clean     清理Docker资源
echo   deploy    完整部署流程 (构建+运行)
echo   help      显示帮助信息
echo.
echo 示例:
echo   %0 build
echo   %0 run
echo   %0 deploy
echo.
exit /b 0

:check_docker
echo %BLUE%[INFO]%NC% 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Docker 未安装，请先安装Docker Desktop
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Docker 服务未启动，请启动Docker Desktop
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Docker 环境正常
exit /b 0

:check_config
if not exist "config\.env" (
    echo %YELLOW%[WARNING]%NC% 配置文件不存在: config\.env
    if not exist "config\.env.example" (
        echo %RED%[ERROR]%NC% 配置示例文件不存在，请检查项目结构
        exit /b 1
    )
    echo %BLUE%[INFO]%NC% 正在创建默认配置文件...
    copy "config\.env.example" "config\.env" >nul
    echo %GREEN%[SUCCESS]%NC% 已创建默认配置文件，请根据需要修改: config\.env
)
exit /b 0

:build_image
echo %BLUE%[INFO]%NC% 开始构建Docker镜像...
echo %BLUE%[INFO]%NC% 镜像名称: %IMAGE_NAME%:%IMAGE_TAG%

REM 切换到项目根目录
cd ..

REM 构建镜像
docker build -t %IMAGE_NAME%:%IMAGE_TAG% .
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 镜像构建失败
    cd docker
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% 镜像构建成功: %IMAGE_NAME%:%IMAGE_TAG%

REM 返回docker目录
cd docker
exit /b 0

:stop_container
echo %BLUE%[INFO]%NC% 停止容器: %CONTAINER_NAME%

REM 检查容器是否运行
docker ps -q -f name=%CONTAINER_NAME% >nul 2>&1
if not errorlevel 1 (
    echo %BLUE%[INFO]%NC% 停止现有容器: %CONTAINER_NAME%
    docker stop %CONTAINER_NAME% >nul
)

REM 检查容器是否存在
docker ps -aq -f name=%CONTAINER_NAME% >nul 2>&1
if not errorlevel 1 (
    echo %BLUE%[INFO]%NC% 删除现有容器: %CONTAINER_NAME%
    docker rm %CONTAINER_NAME% >nul
)

echo %GREEN%[SUCCESS]%NC% 容器已停止
exit /b 0

:run_container
echo %BLUE%[INFO]%NC% 启动Docker容器...
echo %BLUE%[INFO]%NC% 容器名称: %CONTAINER_NAME%
echo %BLUE%[INFO]%NC% 端口映射: %HOST_PORT%:%CONTAINER_PORT%

REM 检查配置文件
call :check_config
if errorlevel 1 exit /b 1

REM 停止现有容器
call :stop_container

REM 获取当前目录的绝对路径
set CURRENT_DIR=%CD%

REM 运行容器
docker run -d --name %CONTAINER_NAME% -p %HOST_PORT%:%CONTAINER_PORT% -v "%CURRENT_DIR%\config\.env:/app/config/.env:ro" --restart unless-stopped %IMAGE_NAME%:%IMAGE_TAG%
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 容器启动失败
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% 容器启动成功
echo %BLUE%[INFO]%NC% 访问地址: http://localhost:%HOST_PORT%

REM 等待容器启动
timeout /t 3 /nobreak >nul

REM 检查容器状态
docker ps -f name=%CONTAINER_NAME% | findstr %CONTAINER_NAME% >nul
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 容器启动失败，请检查日志
    docker logs %CONTAINER_NAME%
    exit /b 1
) else (
    echo %GREEN%[SUCCESS]%NC% 容器运行正常
)

exit /b 0

:restart_container
echo %BLUE%[INFO]%NC% 重启容器...
call :stop_container
call :run_container
exit /b 0

:show_logs
docker ps -q -f name=%CONTAINER_NAME% >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 容器未运行: %CONTAINER_NAME%
    exit /b 1
)

echo %BLUE%[INFO]%NC% 显示容器日志: %CONTAINER_NAME%
docker logs -f %CONTAINER_NAME%
exit /b 0

:clean_resources
echo %BLUE%[INFO]%NC% 清理Docker资源...

REM 停止并删除容器
call :stop_container

REM 删除镜像
docker images -q %IMAGE_NAME% >nul 2>&1
if not errorlevel 1 (
    echo %BLUE%[INFO]%NC% 删除镜像: %IMAGE_NAME%
    docker rmi %IMAGE_NAME%:%IMAGE_TAG% >nul 2>&1
)

REM 清理未使用的资源
echo %BLUE%[INFO]%NC% 清理未使用的Docker资源...
docker system prune -f >nul

echo %GREEN%[SUCCESS]%NC% 清理完成
exit /b 0

:deploy
echo %BLUE%[INFO]%NC% 开始完整部署流程...
call :build_image
if errorlevel 1 exit /b 1
call :run_container
if errorlevel 1 exit /b 1
echo %GREEN%[SUCCESS]%NC% 部署完成！
exit /b 0
