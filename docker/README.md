# YYHIS Web Docker 部署指南

## 🚀 快速开始

### 1. 构建Docker镜像

```bash
# 在项目根目录执行
docker build -t yyhis-web:latest .
```

### 2. 准备配置文件

```bash
# 复制配置示例
cp docker/config/.env.example docker/config/.env

# 编辑配置文件
vim docker/config/.env
```

### 3. 启动容器

#### 方式一：使用docker run

```bash
# 基础启动
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  -v $(pwd)/docker/config/.env:/app/config/.env:ro \
  yyhis-web:latest

# 完整配置启动
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  -v $(pwd)/docker/config/.env:/app/config/.env:ro \
  -e API_PROXY_PASS=http://your-backend-server:6596 \
  -e CLIENT_MAX_BODY_SIZE=20m \
  --restart unless-stopped \
  yyhis-web:latest
```

#### 方式二：使用docker-compose

```bash
cd docker
docker-compose up -d
```

### 4. 访问应用

打开浏览器访问：http://localhost:8080

## 📋 配置说明

### 环境变量配置

| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `VITE_APP_TITLE` | 应用标题 | 测试医院管理系统 | 某某医院管理系统 |
| `VITE_APP_ENV` | 应用环境 | production | development/test/production |
| `VITE_BACKEND_URL` | 后端服务地址 | http://localhost:6596 | https://api.hospital.com |
| `VITE_API_BASE_URL` | API基础路径 | /api | /api |
| `API_PROXY_PASS` | Nginx代理目标 | http://localhost:6596 | http://backend:6596 |
| `NGINX_PORT` | Nginx端口 | 80 | 80 |
| `CLIENT_MAX_BODY_SIZE` | 最大请求体 | 10m | 20m |

### 第三方系统配置

```bash
# 迎春花质控系统
VITE_YINGCHUNHUA_SDK_URL=http://**************:8094/client_app_iframe/index.js
VITE_YINGCHUNHUA_APP_KEY=your-app-key
VITE_YINGCHUNHUA_APP_SECRET_KEY=your-secret-key
```

## 🔧 部署场景

### 场景1：开发环境

```bash
# docker/config/.env
VITE_APP_ENV=development
VITE_BACKEND_URL=http://localhost:6596
API_PROXY_PASS=http://host.docker.internal:6596
```

### 场景2：测试环境

```bash
# docker/config/.env
VITE_APP_ENV=test
VITE_APP_TITLE=测试医院管理系统(测试)
VITE_BACKEND_URL=http://***********:9090
API_PROXY_PASS=http://***********:9090
```

### 场景3：生产环境

```bash
# docker/config/.env
VITE_APP_ENV=production
VITE_BACKEND_URL=https://api.yyhospital.com
API_PROXY_PASS=https://api.yyhospital.com
```

## 🛠️ 高级配置

### 自定义Nginx配置

```bash
# 挂载自定义nginx配置
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  -v $(pwd)/docker/config/.env:/app/config/.env:ro \
  -v $(pwd)/custom-nginx.conf:/etc/nginx/conf.d/default.conf:ro \
  yyhis-web:latest
```

### 多端口部署

```bash
# 部署多个实例
docker run -d --name yyhis-web-test -p 8081:80 -v $(pwd)/test.env:/app/config/.env:ro yyhis-web:latest
docker run -d --name yyhis-web-prod -p 8082:80 -v $(pwd)/prod.env:/app/config/.env:ro yyhis-web:latest
```

### 负载均衡

```yaml
# docker-compose.yml
version: '3.8'
services:
  nginx-lb:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf
    depends_on:
      - web1
      - web2
  
  web1:
    image: yyhis-web:latest
    volumes:
      - ./config/.env:/app/config/.env:ro
  
  web2:
    image: yyhis-web:latest
    volumes:
      - ./config/.env:/app/config/.env:ro
```

## 🔍 故障排除

### 查看日志

```bash
# 查看容器日志
docker logs yyhis-web

# 实时查看日志
docker logs -f yyhis-web
```

### 进入容器调试

```bash
# 进入容器
docker exec -it yyhis-web sh

# 检查nginx配置
docker exec yyhis-web nginx -t

# 检查环境变量
docker exec yyhis-web env | grep VITE
```

### 健康检查

```bash
# 检查健康状态
docker inspect yyhis-web | grep Health -A 10

# 手动健康检查
curl http://localhost:8080/health
```

## 📦 镜像分发

### 导出镜像

```bash
# 导出镜像
docker save yyhis-web:latest | gzip > yyhis-web-latest.tar.gz
```

### 导入镜像

```bash
# 导入镜像
docker load < yyhis-web-latest.tar.gz
```

### 推送到仓库

```bash
# 标记镜像
docker tag yyhis-web:latest your-registry.com/yyhis-web:latest

# 推送镜像
docker push your-registry.com/yyhis-web:latest
```

## ⚠️ 注意事项

1. **配置文件安全**：确保配置文件中的敏感信息（如密钥）安全存储
2. **网络配置**：确保容器能够访问后端服务
3. **资源限制**：生产环境建议设置内存和CPU限制
4. **日志管理**：配置日志轮转避免磁盘空间不足
5. **备份策略**：定期备份配置文件和重要数据

## 🆘 支持

如有问题，请检查：
1. 配置文件格式是否正确
2. 网络连接是否正常
3. 后端服务是否可访问
4. Docker版本是否兼容
