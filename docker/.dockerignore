# 排除不需要的文件和目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 开发工具配置
.vscode
.idea
*.swp
*.swo

# 操作系统文件
.DS_Store
Thumbs.db

# 日志文件
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage
.nyc_output

# 依赖目录
jspm_packages/

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 输出目录（构建时会重新生成）
dist

# 环境变量文件（安全考虑）
.env.local
.env.*.local

# 测试文件
test/
tests/
__tests__/

# 文档
docs/
*.md
!README.md

# Git相关
.git
.gitignore

# Docker相关
Dockerfile*
docker-compose*
.dockerignore
