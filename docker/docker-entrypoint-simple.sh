#!/bin/bash
set -e

echo "🚀 启动 YYHIS Web Docker 容器 (预构建环境)..."

# 显示当前配置
echo "🔧 当前配置："
echo "   - Nginx端口: ${NGINX_PORT}"
echo "   - API代理地址: ${API_PROXY_PASS}"
echo "   - 最大请求体大小: ${CLIENT_MAX_BODY_SIZE}"

# 处理nginx配置模板
echo "📝 生成Nginx配置..."
envsubst '${NGINX_PORT} ${API_PROXY_PASS} ${CLIENT_MAX_BODY_SIZE}' < /etc/nginx/templates/default.conf.template > /etc/nginx/conf.d/default.conf

# 验证nginx配置
echo "✅ 验证Nginx配置..."
nginx -t

echo "🎉 配置完成，启动服务..."

# 执行传入的命令
exec "$@"
