#!/bin/bash

# YYHIS Web Docker 构建脚本
# 用于自动化构建和部署Docker镜像

set -e

# 设置脚本为可执行
chmod +x "$0" 2>/dev/null || true

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
IMAGE_NAME="yyhis-web"
IMAGE_TAG="latest"
CONTAINER_NAME="yyhis-web"
HOST_PORT="8080"
CONTAINER_PORT="80"

# 显示帮助信息
show_help() {
    echo "YYHIS Web Docker 构建脚本"
    echo ""
    echo "用法: $0 [选项] [命令]"
    echo ""
    echo "命令:"
    echo "  build     构建Docker镜像"
    echo "  run       运行Docker容器"
    echo "  stop      停止Docker容器"
    echo "  restart   重启Docker容器"
    echo "  logs      查看容器日志"
    echo "  clean     清理Docker资源"
    echo "  deploy    完整部署流程 (构建+运行)"
    echo ""
    echo "选项:"
    echo "  -t, --tag TAG        镜像标签 (默认: latest)"
    echo "  -n, --name NAME      容器名称 (默认: yyhis-web)"
    echo "  -p, --port PORT      主机端口 (默认: 8080)"
    echo "  -e, --env ENV        环境类型 (dev/test/prod)"
    echo "  -h, --help           显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build -t v1.0.0"
    echo "  $0 run -p 9090 -e test"
    echo "  $0 deploy -t v1.0.0 -p 8080"
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未启动，请启动Docker服务"
        exit 1
    fi
}

# 检查配置文件
check_config() {
    local env_file="./config/.env"
    
    if [ ! -f "$env_file" ]; then
        log_warning "配置文件不存在: $env_file"
        log_info "正在创建默认配置文件..."
        
        if [ ! -f "./config/.env.example" ]; then
            log_error "配置示例文件不存在，请检查项目结构"
            exit 1
        fi
        
        cp "./config/.env.example" "$env_file"
        log_success "已创建默认配置文件，请根据需要修改: $env_file"
    fi
}

# 构建镜像
build_image() {
    log_info "开始构建Docker镜像..."
    log_info "镜像名称: ${IMAGE_NAME}:${IMAGE_TAG}"
    
    # 切换到项目根目录
    cd ..
    
    # 构建镜像
    if docker build -t "${IMAGE_NAME}:${IMAGE_TAG}" .; then
        log_success "镜像构建成功: ${IMAGE_NAME}:${IMAGE_TAG}"
    else
        log_error "镜像构建失败"
        exit 1
    fi
    
    # 返回docker目录
    cd docker
}

# 停止并删除现有容器
stop_container() {
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_info "停止现有容器: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME"
    fi
    
    if docker ps -aq -f name="$CONTAINER_NAME" | grep -q .; then
        log_info "删除现有容器: $CONTAINER_NAME"
        docker rm "$CONTAINER_NAME"
    fi
}

# 运行容器
run_container() {
    log_info "启动Docker容器..."
    log_info "容器名称: $CONTAINER_NAME"
    log_info "端口映射: $HOST_PORT:$CONTAINER_PORT"
    
    # 检查配置文件
    check_config
    
    # 停止现有容器
    stop_container
    
    # 构建docker run命令
    local docker_cmd="docker run -d"
    docker_cmd="$docker_cmd --name $CONTAINER_NAME"
    docker_cmd="$docker_cmd -p $HOST_PORT:$CONTAINER_PORT"
    docker_cmd="$docker_cmd -v $(pwd)/config/.env:/app/config/.env:ro"
    docker_cmd="$docker_cmd --restart unless-stopped"
    
    # 根据环境设置额外参数
    if [ -n "$ENV_TYPE" ]; then
        case "$ENV_TYPE" in
            "dev")
                docker_cmd="$docker_cmd -e API_PROXY_PASS=http://host.docker.internal:6596"
                ;;
            "test")
                docker_cmd="$docker_cmd -e API_PROXY_PASS=http://***********:9090"
                ;;
            "prod")
                docker_cmd="$docker_cmd -e API_PROXY_PASS=https://api.yyhospital.com"
                ;;
        esac
    fi
    
    docker_cmd="$docker_cmd ${IMAGE_NAME}:${IMAGE_TAG}"
    
    # 执行命令
    if eval $docker_cmd; then
        log_success "容器启动成功"
        log_info "访问地址: http://localhost:$HOST_PORT"
        
        # 等待容器启动
        sleep 3
        
        # 检查容器状态
        if docker ps -f name="$CONTAINER_NAME" | grep -q "$CONTAINER_NAME"; then
            log_success "容器运行正常"
        else
            log_error "容器启动失败，请检查日志"
            docker logs "$CONTAINER_NAME"
        fi
    else
        log_error "容器启动失败"
        exit 1
    fi
}

# 查看日志
show_logs() {
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_info "显示容器日志: $CONTAINER_NAME"
        docker logs -f "$CONTAINER_NAME"
    else
        log_error "容器未运行: $CONTAINER_NAME"
        exit 1
    fi
}

# 清理资源
clean_resources() {
    log_info "清理Docker资源..."
    
    # 停止并删除容器
    stop_container
    
    # 删除镜像
    if docker images -q "$IMAGE_NAME" | grep -q .; then
        log_info "删除镜像: $IMAGE_NAME"
        docker rmi "$IMAGE_NAME:$IMAGE_TAG" 2>/dev/null || true
    fi
    
    # 清理未使用的资源
    log_info "清理未使用的Docker资源..."
    docker system prune -f
    
    log_success "清理完成"
}

# 完整部署
deploy() {
    log_info "开始完整部署流程..."
    build_image
    run_container
    log_success "部署完成！"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -n|--name)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        -p|--port)
            HOST_PORT="$2"
            shift 2
            ;;
        -e|--env)
            ENV_TYPE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        build|run|stop|restart|logs|clean|deploy)
            COMMAND="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查Docker环境
check_docker

# 执行命令
case "${COMMAND:-}" in
    "build")
        build_image
        ;;
    "run")
        run_container
        ;;
    "stop")
        stop_container
        log_success "容器已停止"
        ;;
    "restart")
        stop_container
        run_container
        ;;
    "logs")
        show_logs
        ;;
    "clean")
        clean_resources
        ;;
    "deploy")
        deploy
        ;;
    "")
        log_error "请指定命令"
        show_help
        exit 1
        ;;
    *)
        log_error "未知命令: $COMMAND"
        show_help
        exit 1
        ;;
esac
