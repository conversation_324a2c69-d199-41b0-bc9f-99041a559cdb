#!/bin/bash

# YYHIS Web 多环境Docker构建脚本
# 为不同环境构建专用镜像，配置在构建时固化

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
IMAGE_NAME="yyhis-web"
BUILD_ENV="production"

# 显示帮助信息
show_help() {
    echo "YYHIS Web 多环境Docker构建脚本"
    echo ""
    echo "用法: $0 [选项] [环境]"
    echo ""
    echo "环境:"
    echo "  dev         开发环境"
    echo "  test        测试环境"
    echo "  prod        生产环境 (默认)"
    echo ""
    echo "选项:"
    echo "  -n, --name NAME      镜像名称 (默认: yyhis-web)"
    echo "  -h, --help           显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 test              # 构建测试环境镜像"
    echo "  $0 prod -n my-app    # 构建生产环境镜像，自定义名称"
    echo "  $0 dev               # 构建开发环境镜像"
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未启动，请启动Docker服务"
        exit 1
    fi
}

# 验证环境配置文件
validate_env_file() {
    local env_file=".env.$1"
    
    if [ ! -f "$env_file" ]; then
        log_error "环境配置文件不存在: $env_file"
        exit 1
    fi
    
    log_info "验证环境配置文件: $env_file"
    
    # 检查必要的配置项
    local required_vars=(
        "VITE_APP_TITLE"
        "VITE_APP_ENV"
        "VITE_API_BASE_URL"
        "VITE_BACKEND_URL"
    )
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$env_file"; then
            log_warning "配置项 $var 在 $env_file 中未找到"
        fi
    done
    
    log_success "环境配置文件验证完成"
}

# 构建镜像
build_image() {
    local env=$1
    local tag="${IMAGE_NAME}:${env}"
    
    log_info "开始构建Docker镜像..."
    log_info "构建环境: $env"
    log_info "镜像标签: $tag"
    
    # 切换到项目根目录
    cd ..
    
    # 验证环境配置文件
    validate_env_file "$env"
    
    # 显示将要使用的配置
    log_info "环境配置预览:"
    echo "----------------------------------------"
    grep "^VITE_" ".env.$env" | head -10
    echo "----------------------------------------"
    
    # 构建镜像
    if docker build \
        --build-arg BUILD_ENV="$env" \
        --build-arg NODE_VERSION="20.19.0" \
        -f Dockerfile.multi-env \
        -t "$tag" \
        .; then
        log_success "镜像构建成功: $tag"
        
        # 显示镜像信息
        log_info "镜像信息:"
        docker images "$IMAGE_NAME" | grep "$env"
        
        # 显示镜像标签
        log_info "镜像标签信息:"
        docker inspect "$tag" --format='{{range .Config.Labels}}{{println .}}{{end}}' | grep -E "(build\.|description)"
        
    else
        log_error "镜像构建失败"
        exit 1
    fi
    
    # 返回docker目录
    cd docker
}

# 构建所有环境
build_all() {
    log_info "构建所有环境的镜像..."
    
    local environments=("development" "test" "production")
    
    for env in "${environments[@]}"; do
        log_info "构建环境: $env"
        build_image "$env"
        echo ""
    done
    
    log_success "所有环境镜像构建完成！"
    
    # 显示所有构建的镜像
    log_info "构建的镜像列表:"
    docker images "$IMAGE_NAME"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        dev|development)
            BUILD_ENV="development"
            shift
            ;;
        test)
            BUILD_ENV="test"
            shift
            ;;
        prod|production)
            BUILD_ENV="production"
            shift
            ;;
        all)
            build_all
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查Docker环境
check_docker

# 构建指定环境的镜像
build_image "$BUILD_ENV"

log_success "构建完成！"
log_info "使用方法:"
echo "  docker run -d --name yyhis-web-$BUILD_ENV -p 8080:80 ${IMAGE_NAME}:${BUILD_ENV}"
