version: '3.8'

services:
  yyhis-web:
    build:
      context: ..
      dockerfile: Dockerfile
    container_name: yyhis-web
    ports:
      - "8080:80"
    volumes:
      # 挂载外部配置文件
      - ./config/.env:/app/config/.env:ro
      # 可选：挂载自定义nginx配置
      # - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
    environment:
      # 基础配置
      - NGINX_PORT=80
      - CLIENT_MAX_BODY_SIZE=10m
      # API代理配置（可以通过环境变量覆盖）
      - API_PROXY_PASS=http://host.docker.internal:6596
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - yyhis-network

  # 可选：如果需要本地后端服务
  # yyhis-backend:
  #   image: your-backend-image:latest
  #   container_name: yyhis-backend
  #   ports:
  #     - "6596:6596"
  #   environment:
  #     - NODE_ENV=production
  #   networks:
  #     - yyhis-network

networks:
  yyhis-network:
    driver: bridge

# 可选：数据卷
# volumes:
#   yyhis-data:
