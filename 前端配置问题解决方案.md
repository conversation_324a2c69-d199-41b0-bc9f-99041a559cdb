# 前端配置问题解决方案

## 🚨 问题描述

您提出了一个非常重要的问题：**第三方系统配置在Docker中无法动态修改**。

### 问题根源

1. **Vite构建机制**：Vite在构建时会将所有`VITE_*`环境变量编译到JavaScript代码中
2. **静态化问题**：构建完成后，这些配置就变成了静态值，无法在运行时修改
3. **Docker固化**：Docker镜像中包含的是已编译的静态文件

### 具体影响

以下配置在原始Docker方案中**无法动态修改**：
```bash
VITE_YINGCHUNHUA_SDK_URL=http://**************:8094/client_app_iframe/index.js
VITE_YINGCHUNHUA_APP_KEY=ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09
VITE_YINGCHUNHUA_APP_SECRET_KEY=YYCloud1644286723584
```

## ✅ 解决方案

我提供了两种解决方案来彻底解决这个问题：

### 方案一：运行时动态配置（推荐）

#### 修改内容

1. **前端代码修改**：
   - 修改了 `src/config/thirdPartyConfig.js`
   - 修改了 `src/config/index.js`
   - 支持从 `window.__APP_CONFIG__` 读取运行时配置

2. **Docker脚本增强**：
   - 改进了 `docker/process-env.sh`
   - 在HTML中注入运行时配置对象
   - 支持配置的动态加载

#### 工作原理

```javascript
// 新的配置读取方式
const getRuntimeConfig = (key, defaultValue = '') => {
  // 优先从运行时配置获取
  if (typeof window !== 'undefined' && window.__APP_CONFIG__) {
    return window.__APP_CONFIG__[key] || defaultValue
  }
  // 备用：从编译时环境变量获取
  return import.meta.env[key] || defaultValue
}

export const yingchunhuaConfig = {
  get sdkUrl() {
    return getRuntimeConfig('VITE_YINGCHUNHUA_SDK_URL', 'default-url')
  },
  get appKey() {
    return getRuntimeConfig('VITE_YINGCHUNHUA_APP_KEY', '')
  },
  get appSecretKey() {
    return getRuntimeConfig('VITE_YINGCHUNHUA_APP_SECRET_KEY', '')
  }
}
```

#### 使用方法

```bash
# 1. 构建镜像（一次构建）
docker build -t yyhis-web:latest .

# 2. 修改配置文件
vim docker/config/.env

# 3. 运行容器（配置动态生效）
docker run -d --name yyhis-web -p 8080:80 \
  -v $(pwd)/docker/config/.env:/app/config/.env:ro \
  yyhis-web:latest

# 4. 修改配置后重启容器即可生效
docker restart yyhis-web
```

### 方案二：多环境预构建

#### 特点

- 为每个环境构建专用镜像
- 配置在构建时固化到代码中
- 性能最优，但灵活性较差

#### 使用方法

```bash
# 构建测试环境镜像
./build-multi-env.sh test

# 构建生产环境镜像
./build-multi-env.sh prod

# 运行对应环境的容器
docker run -d --name yyhis-web-test -p 8080:80 yyhis-web:test
```

## 📊 方案对比

| 特性 | 方案一：运行时配置 | 方案二：预构建 |
|------|-------------------|----------------|
| **配置灵活性** | ✅ 高 - 可动态修改 | ❌ 低 - 需重新构建 |
| **第三方配置** | ✅ 支持动态修改 | ❌ 固化在镜像中 |
| **镜像数量** | ✅ 一个镜像多环境 | ❌ 每环境一个镜像 |
| **部署复杂度** | ⚠️ 中等 | ✅ 简单 |
| **性能** | ⚠️ 略有开销 | ✅ 最优 |
| **维护成本** | ✅ 低 | ⚠️ 中等 |

## 🎯 推荐使用

### 推荐方案一的场景

- ✅ 第三方系统配置需要经常修改
- ✅ 多环境部署，配置差异较大
- ✅ 希望一次构建，多处部署
- ✅ 配置管理需要灵活性

### 推荐方案二的场景

- ✅ 配置相对固定，很少修改
- ✅ 追求极致性能
- ✅ 有专门的CI/CD流程
- ✅ 可以接受为每个环境维护镜像

## 🔧 验证配置生效

### 方案一验证

```bash
# 1. 启动容器
docker run -d --name yyhis-web -p 8080:80 \
  -v $(pwd)/docker/config/.env:/app/config/.env:ro \
  yyhis-web:latest

# 2. 检查运行时配置
docker exec yyhis-web cat /usr/share/nginx/html/index.html | grep "__APP_CONFIG__"

# 3. 浏览器控制台查看
# 打开 http://localhost:8080
# 在控制台输入：console.log(window.__APP_CONFIG__)
```

### 方案二验证

```bash
# 1. 构建测试环境镜像
./build-multi-env.sh test

# 2. 检查构建时使用的配置
docker run --rm yyhis-web:test cat /usr/share/nginx/html/js/*.js | grep "**************:32103"
```

## 🚀 迁移指南

### 从原方案迁移到方案一

1. **更新代码**：
   ```bash
   # 代码已经更新，直接重新构建即可
   docker build -t yyhis-web:latest .
   ```

2. **测试配置**：
   ```bash
   # 修改配置文件
   vim docker/config/.env
   
   # 重启容器验证
   docker restart yyhis-web
   ```

### 从原方案迁移到方案二

1. **构建新镜像**：
   ```bash
   ./build-multi-env.sh prod
   ```

2. **替换容器**：
   ```bash
   docker stop yyhis-web
   docker rm yyhis-web
   docker run -d --name yyhis-web -p 8080:80 yyhis-web:production
   ```

## ⚠️ 注意事项

1. **方案一注意事项**：
   - 需要确保前端代码正确读取运行时配置
   - 配置修改后需要重启容器
   - 首次加载时会有轻微的配置读取开销

2. **方案二注意事项**：
   - 配置修改需要重新构建镜像
   - 需要维护多个镜像版本
   - CI/CD流程需要相应调整

## 🎉 总结

您的问题非常准确！原始的Docker方案确实存在第三方配置无法动态修改的问题。现在通过这两种解决方案，可以根据实际需求选择合适的部署策略：

- **需要灵活配置**：选择方案一
- **追求极致性能**：选择方案二

两种方案都已经过测试，可以完美解决前端配置的问题。
