# YYHIS Web Docker 快速开始

## 🚀 5分钟快速部署

### 1. 检查环境

确保已安装 Docker Desktop 并正在运行。

### 2. 修改配置

编辑 `docker/config/.env` 文件，主要修改以下配置：

```bash
# 修改后端服务器地址
VITE_BACKEND_URL=http://your-backend-server:6596
API_PROXY_PASS=http://your-backend-server:6596

# 如果后端在本机，使用：
API_PROXY_PASS=http://host.docker.internal:6596
```

### 3. 一键部署

#### Windows 用户：
```cmd
cd docker
build.bat deploy
```

#### Linux/Mac 用户：
```bash
cd docker
chmod +x build.sh
./build.sh deploy
```

### 4. 访问应用

打开浏览器访问：http://localhost:8080

## 📋 常用命令

### Windows (build.bat)
```cmd
build.bat build     # 构建镜像
build.bat run       # 运行容器
build.bat stop      # 停止容器
build.bat logs      # 查看日志
build.bat clean     # 清理资源
```

### Linux/Mac (build.sh)
```bash
./build.sh build     # 构建镜像
./build.sh run       # 运行容器
./build.sh stop      # 停止容器
./build.sh logs      # 查看日志
./build.sh clean     # 清理资源
```

## 🔧 配置说明

### 关键配置项

| 配置项 | 说明 | 示例 |
|--------|------|------|
| `VITE_BACKEND_URL` | 后端服务地址 | `http://localhost:6596` |
| `API_PROXY_PASS` | Docker内代理地址 | `http://host.docker.internal:6596` |
| `VITE_APP_TITLE` | 应用标题 | `某某医院管理系统` |

### 环境配置示例

#### 开发环境
```bash
VITE_APP_ENV=development
VITE_BACKEND_URL=http://localhost:6596
API_PROXY_PASS=http://host.docker.internal:6596
```

#### 测试环境
```bash
VITE_APP_ENV=test
VITE_BACKEND_URL=http://***********:9090
API_PROXY_PASS=http://***********:9090
```

#### 生产环境
```bash
VITE_APP_ENV=production
VITE_BACKEND_URL=https://api.yyhospital.com
API_PROXY_PASS=https://api.yyhospital.com
```

## 🆘 常见问题

### Q: 容器启动失败？
```bash
# 查看日志
docker logs yyhis-web

# 检查配置文件
cat docker/config/.env
```

### Q: 无法访问后端API？
1. 检查 `API_PROXY_PASS` 配置是否正确
2. 确认后端服务是否正常运行
3. 检查网络连接

### Q: 配置修改不生效？
```bash
# 重启容器
docker restart yyhis-web
```

## 📦 分发给其他用户

### 1. 导出镜像
```bash
docker save yyhis-web:latest | gzip > yyhis-web.tar.gz
```

### 2. 打包分发文件
- `yyhis-web.tar.gz` (Docker镜像)
- `docker/config/.env.example` (配置示例)
- `快速开始.md` (本文档)

### 3. 其他用户使用
```bash
# 导入镜像
docker load < yyhis-web.tar.gz

# 准备配置
cp .env.example .env
# 编辑 .env 文件

# 启动容器
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  -v $(pwd)/.env:/app/config/.env:ro \
  --restart unless-stopped \
  yyhis-web:latest
```

## 📞 技术支持

如有问题，请：
1. 查看详细文档：`Docker部署指南.md`
2. 检查容器日志：`docker logs yyhis-web`
3. 验证配置文件格式和内容
