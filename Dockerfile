# 多阶段构建 Dockerfile for YYHIS Web
# 第一阶段：构建阶段
FROM node:20.19.0-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production --silent

# 复制源代码
COPY . .

# 创建默认环境配置文件（如果不存在）
RUN if [ ! -f .env.production ]; then \
    echo "# 生产环境配置" > .env.production && \
    echo "VITE_APP_TITLE=测试医院管理系统" >> .env.production && \
    echo "VITE_APP_ENV=production" >> .env.production && \
    echo "VITE_API_BASE_URL=/api" >> .env.production && \
    echo "VITE_API_TIMEOUT=15000" >> .env.production && \
    echo "VITE_BACKEND_URL=http://localhost:6596" >> .env.production && \
    echo "VITE_APP_VERSION=1.0.0" >> .env.production; \
    fi

# 构建应用
RUN npm run build:prod

# 第二阶段：运行阶段
FROM nginx:1.25-alpine

# 安装必要工具
RUN apk add --no-cache bash envsubst

# 创建配置目录
RUN mkdir -p /app/config

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置模板
COPY docker/nginx.conf.template /etc/nginx/templates/default.conf.template

# 复制启动脚本
COPY docker/docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# 复制环境配置处理脚本
COPY docker/process-env.sh /usr/local/bin/process-env.sh
RUN chmod +x /usr/local/bin/process-env.sh

# 设置默认环境变量
ENV NGINX_PORT=80
ENV API_PROXY_PASS=http://localhost:6596
ENV CLIENT_MAX_BODY_SIZE=10m

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${NGINX_PORT}/ || exit 1

# 启动脚本
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
