# YYHIS Web Docker 部署完整指南

## 📋 概述

本指南提供了YYHIS Web前端项目的完整Docker部署方案，支持配置文件外部化，使得用户可以通过修改配置文件来自定义部署参数，而无需重新构建镜像。

## 🎯 特性

- ✅ **多阶段构建**：优化镜像大小，提高构建效率
- ✅ **配置外部化**：支持通过挂载卷动态配置
- ✅ **环境适配**：支持开发、测试、生产多环境
- ✅ **自动代理**：内置Nginx反向代理配置
- ✅ **健康检查**：内置容器健康监控
- ✅ **安全优化**：最小化镜像，安全头配置
- ✅ **易于分发**：一次构建，到处运行

## 🚀 快速开始

### 1. 项目结构

```
yyhis-web/
├── Dockerfile                 # Docker镜像构建文件
├── docker/
│   ├── README.md             # Docker详细说明
│   ├── build.sh              # 自动化构建脚本
│   ├── docker-compose.yml    # Docker Compose配置
│   ├── nginx.conf.template   # Nginx配置模板
│   ├── docker-entrypoint.sh  # 容器启动脚本
│   ├── process-env.sh        # 环境配置处理脚本
│   ├── .dockerignore         # Docker忽略文件
│   └── config/
│       ├── .env.example      # 配置文件示例
│       └── .env              # 实际配置文件（需要创建）
└── ... (其他项目文件)
```

### 2. 准备配置文件

```bash
# 复制配置示例
cp docker/config/.env.example docker/config/.env

# 编辑配置文件
vim docker/config/.env
```

### 3. 构建和运行

#### 方式一：使用自动化脚本（推荐）

```bash
# 进入docker目录
cd docker

# 给脚本执行权限
chmod +x build.sh

# 完整部署（构建+运行）
./build.sh deploy

# 或者分步执行
./build.sh build          # 构建镜像
./build.sh run            # 运行容器

# 查看日志
./build.sh logs

# 停止容器
./build.sh stop
```

#### 方式二：使用Docker命令

```bash
# 构建镜像
docker build -t yyhis-web:latest .

# 运行容器
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  -v $(pwd)/docker/config/.env:/app/config/.env:ro \
  --restart unless-stopped \
  yyhis-web:latest
```

#### 方式三：使用Docker Compose

```bash
cd docker
docker-compose up -d
```

### 4. 访问应用

打开浏览器访问：http://localhost:8080

## ⚙️ 配置说明

### 核心配置项

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| `VITE_APP_TITLE` | 应用标题 | `测试医院管理系统` |
| `VITE_APP_ENV` | 应用环境 | `production` |
| `VITE_BACKEND_URL` | 后端服务地址 | `http://localhost:6596` |
| `VITE_API_BASE_URL` | API基础路径 | `/api` |
| `API_PROXY_PASS` | Nginx代理目标 | `http://host.docker.internal:6596` |

### 第三方系统配置

```bash
# 迎春花质控系统配置
VITE_YINGCHUNHUA_SDK_URL=http://**************:8094/client_app_iframe/index.js
VITE_YINGCHUNHUA_APP_KEY=your-app-key
VITE_YINGCHUNHUA_APP_SECRET_KEY=your-secret-key
```

## 🌍 多环境部署

### 开发环境

```bash
# docker/config/.env
VITE_APP_ENV=development
VITE_APP_TITLE=测试医院管理系统(开发)
VITE_BACKEND_URL=http://localhost:6596
API_PROXY_PASS=http://host.docker.internal:6596

# 启动
./build.sh deploy -e dev -p 8080
```

### 测试环境

```bash
# docker/config/.env
VITE_APP_ENV=test
VITE_APP_TITLE=测试医院管理系统(测试)
VITE_BACKEND_URL=http://***********:9090
API_PROXY_PASS=http://***********:9090

# 启动
./build.sh deploy -e test -p 8081
```

### 生产环境

```bash
# docker/config/.env
VITE_APP_ENV=production
VITE_APP_TITLE=测试医院管理系统
VITE_BACKEND_URL=https://api.yyhospital.com
API_PROXY_PASS=https://api.yyhospital.com

# 启动
./build.sh deploy -e prod -p 8082
```

## 📦 镜像分发

### 1. 导出镜像

```bash
# 构建镜像
docker build -t yyhis-web:v1.0.0 .

# 导出镜像
docker save yyhis-web:v1.0.0 | gzip > yyhis-web-v1.0.0.tar.gz
```

### 2. 分发给其他用户

将以下文件打包分发：
- `yyhis-web-v1.0.0.tar.gz` (Docker镜像)
- `docker/config/.env.example` (配置示例)
- `docker/docker-compose.yml` (可选)
- `Docker部署指南.md` (本文档)

### 3. 其他用户使用

```bash
# 1. 导入镜像
docker load < yyhis-web-v1.0.0.tar.gz

# 2. 准备配置文件
cp .env.example .env
vim .env  # 修改配置

# 3. 启动容器
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  -v $(pwd)/.env:/app/config/.env:ro \
  --restart unless-stopped \
  yyhis-web:v1.0.0
```

## 🔧 高级配置

### 自定义Nginx配置

```bash
# 创建自定义nginx配置
cp docker/nginx.conf.template custom-nginx.conf
vim custom-nginx.conf

# 挂载自定义配置
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  -v $(pwd)/.env:/app/config/.env:ro \
  -v $(pwd)/custom-nginx.conf:/etc/nginx/conf.d/default.conf:ro \
  yyhis-web:latest
```

### 多实例部署

```bash
# 部署测试实例
docker run -d --name yyhis-web-test -p 8081:80 \
  -v $(pwd)/test.env:/app/config/.env:ro yyhis-web:latest

# 部署生产实例  
docker run -d --name yyhis-web-prod -p 8082:80 \
  -v $(pwd)/prod.env:/app/config/.env:ro yyhis-web:latest
```

### 资源限制

```bash
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  --memory=512m \
  --cpus=1.0 \
  -v $(pwd)/.env:/app/config/.env:ro \
  yyhis-web:latest
```

## 🔍 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看日志
   docker logs yyhis-web
   
   # 检查配置文件
   cat docker/config/.env
   ```

2. **API请求失败**
   ```bash
   # 检查后端服务是否可访问
   curl http://your-backend-server:6596/health
   
   # 检查nginx代理配置
   docker exec yyhis-web cat /etc/nginx/conf.d/default.conf
   ```

3. **配置不生效**
   ```bash
   # 重启容器
   docker restart yyhis-web
   
   # 检查环境变量
   docker exec yyhis-web env | grep VITE
   ```

### 调试命令

```bash
# 进入容器
docker exec -it yyhis-web sh

# 检查nginx状态
docker exec yyhis-web nginx -t

# 查看进程
docker exec yyhis-web ps aux

# 检查网络
docker exec yyhis-web netstat -tlnp
```

## 📊 监控和维护

### 健康检查

```bash
# 检查容器健康状态
docker inspect yyhis-web | grep Health -A 10

# 手动健康检查
curl http://localhost:8080/health
```

### 日志管理

```bash
# 查看实时日志
docker logs -f yyhis-web

# 查看最近100行日志
docker logs --tail 100 yyhis-web

# 配置日志轮转
docker run -d \
  --name yyhis-web \
  --log-driver json-file \
  --log-opt max-size=10m \
  --log-opt max-file=3 \
  -p 8080:80 \
  yyhis-web:latest
```

### 备份和恢复

```bash
# 备份配置文件
cp docker/config/.env docker/config/.env.backup.$(date +%Y%m%d)

# 备份容器
docker commit yyhis-web yyhis-web:backup-$(date +%Y%m%d)

# 恢复配置
cp docker/config/.env.backup.20240101 docker/config/.env
docker restart yyhis-web
```

## ⚠️ 安全注意事项

1. **配置文件安全**
   - 不要在配置文件中使用明文密码
   - 限制配置文件的访问权限
   - 定期更换密钥

2. **网络安全**
   - 使用HTTPS进行生产部署
   - 配置防火墙规则
   - 限制容器网络访问

3. **镜像安全**
   - 定期更新基础镜像
   - 扫描镜像漏洞
   - 使用最小权限原则

## 🆘 技术支持

如遇到问题，请按以下步骤排查：

1. 检查Docker版本兼容性
2. 验证配置文件格式
3. 确认网络连接正常
4. 查看容器日志信息
5. 检查资源使用情况

更多详细信息请参考 `docker/README.md` 文件。
