# YYHIS Web Docker 部署完整指南

## 📋 概述

本指南提供了YYHIS Web前端项目的完整Docker部署方案，包含两种不同的部署策略来解决前端配置的问题。

## ⚠️ 重要说明：前端配置的限制

**问题**：Vite构建时会将`VITE_*`环境变量编译到JavaScript代码中，构建完成后这些配置就固化了，无法在运行时修改。

**解决方案**：我们提供了两种方案来解决这个问题：

### 方案一：运行时动态配置（推荐）
- ✅ 修改了前端代码，支持运行时配置读取
- ✅ 一次构建，多环境部署
- ✅ 配置文件外部化，无需重新构建镜像
- ✅ 第三方系统配置可以动态修改

### 方案二：多环境预构建
- ✅ 为不同环境构建专用镜像
- ✅ 配置在构建时固化，性能最优
- ❌ 需要为每个环境单独构建镜像
- ❌ 配置修改需要重新构建

## 🎯 特性

- ✅ **多阶段构建**：优化镜像大小，提高构建效率
- ✅ **配置外部化**：支持通过挂载卷动态配置（方案一）
- ✅ **运行时配置**：第三方系统配置支持动态修改（方案一）
- ✅ **环境适配**：支持开发、测试、生产多环境
- ✅ **自动代理**：内置Nginx反向代理配置
- ✅ **健康检查**：内置容器健康监控
- ✅ **安全优化**：最小化镜像，安全头配置

## 🚀 快速开始

### 选择部署方案

#### 方案一：运行时动态配置（推荐）
适用于需要灵活配置的场景，特别是第三方系统配置需要经常修改的情况。

#### 方案二：多环境预构建
适用于配置相对固定，追求最佳性能的场景。

---

## 📦 方案一：运行时动态配置

### 1. 项目结构

```
yyhis-web/
├── Dockerfile                 # Docker镜像构建文件
├── docker/
│   ├── README.md             # Docker详细说明
│   ├── build.sh              # 自动化构建脚本
│   ├── docker-compose.yml    # Docker Compose配置
│   ├── nginx.conf.template   # Nginx配置模板
│   ├── docker-entrypoint.sh  # 容器启动脚本
│   ├── process-env.sh        # 环境配置处理脚本
│   ├── .dockerignore         # Docker忽略文件
│   └── config/
│       ├── .env.example      # 配置文件示例
│       └── .env              # 实际配置文件（需要创建）
└── ... (其他项目文件)
```

### 2. 准备配置文件

```bash
# 复制配置示例
cp docker/config/.env.example docker/config/.env

# 编辑配置文件
vim docker/config/.env
```

### 3. 构建和运行

#### 方式一：使用自动化脚本（推荐）

```bash
# 进入docker目录
cd docker

# 给脚本执行权限
chmod +x build.sh

# 完整部署（构建+运行）
./build.sh deploy

# 或者分步执行
./build.sh build          # 构建镜像
./build.sh run            # 运行容器

# 查看日志
./build.sh logs

# 停止容器
./build.sh stop
```

#### 方式二：使用Docker命令

```bash
# 构建镜像
docker build -t yyhis-web:latest .

# 运行容器
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  -v $(pwd)/docker/config/.env:/app/config/.env:ro \
  --restart unless-stopped \
  yyhis-web:latest
```

#### 方式三：使用Docker Compose

```bash
cd docker
docker-compose up -d
```

### 4. 访问应用

打开浏览器访问：http://localhost:8080

---

## 📦 方案二：多环境预构建

### 1. 构建不同环境的镜像

```bash
cd docker

# 给脚本执行权限
chmod +x build-multi-env.sh

# 构建测试环境镜像
./build-multi-env.sh test

# 构建生产环境镜像
./build-multi-env.sh prod

# 构建所有环境镜像
./build-multi-env.sh all
```

### 2. 运行特定环境的容器

```bash
# 运行测试环境
docker run -d --name yyhis-web-test -p 8081:80 \
  -e API_PROXY_PASS=http://***********:9090 \
  yyhis-web:test

# 运行生产环境
docker run -d --name yyhis-web-prod -p 8082:80 \
  -e API_PROXY_PASS=https://api.yyhospital.com \
  yyhis-web:production
```

### 3. 优缺点

**优点：**
- ✅ 配置完全固化，性能最优
- ✅ 不需要运行时配置处理
- ✅ 镜像更小，启动更快

**缺点：**
- ❌ 配置修改需要重新构建镜像
- ❌ 需要维护多个镜像版本
- ❌ 第三方系统配置无法动态修改

---

## ⚙️ 配置说明

### 核心配置项

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| `VITE_APP_TITLE` | 应用标题 | `测试医院管理系统` |
| `VITE_APP_ENV` | 应用环境 | `production` |
| `VITE_BACKEND_URL` | 后端服务地址 | `http://localhost:6596` |
| `VITE_API_BASE_URL` | API基础路径 | `/api` |
| `API_PROXY_PASS` | Nginx代理目标 | `http://host.docker.internal:6596` |

### 第三方系统配置

```bash
# 迎春花质控系统配置
VITE_YINGCHUNHUA_SDK_URL=http://**************:8094/client_app_iframe/index.js
VITE_YINGCHUNHUA_APP_KEY=your-app-key
VITE_YINGCHUNHUA_APP_SECRET_KEY=your-secret-key
```

## 🌍 多环境部署

### 开发环境

```bash
# docker/config/.env
VITE_APP_ENV=development
VITE_APP_TITLE=测试医院管理系统(开发)
VITE_BACKEND_URL=http://localhost:6596
API_PROXY_PASS=http://host.docker.internal:6596

# 启动
./build.sh deploy -e dev -p 8080
```

### 测试环境

```bash
# docker/config/.env
VITE_APP_ENV=test
VITE_APP_TITLE=测试医院管理系统(测试)
VITE_BACKEND_URL=http://***********:9090
API_PROXY_PASS=http://***********:9090

# 启动
./build.sh deploy -e test -p 8081
```

### 生产环境

```bash
# docker/config/.env
VITE_APP_ENV=production
VITE_APP_TITLE=测试医院管理系统
VITE_BACKEND_URL=https://api.yyhospital.com
API_PROXY_PASS=https://api.yyhospital.com

# 启动
./build.sh deploy -e prod -p 8082
```

## 📦 镜像分发

### 1. 导出镜像

```bash
# 构建镜像
docker build -t yyhis-web:v1.0.0 .

# 导出镜像
docker save yyhis-web:v1.0.0 | gzip > yyhis-web-v1.0.0.tar.gz
```

### 2. 分发给其他用户

将以下文件打包分发：
- `yyhis-web-v1.0.0.tar.gz` (Docker镜像)
- `docker/config/.env.example` (配置示例)
- `docker/docker-compose.yml` (可选)
- `Docker部署指南.md` (本文档)

### 3. 其他用户使用

```bash
# 1. 导入镜像
docker load < yyhis-web-v1.0.0.tar.gz

# 2. 准备配置文件
cp .env.example .env
vim .env  # 修改配置

# 3. 启动容器
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  -v $(pwd)/.env:/app/config/.env:ro \
  --restart unless-stopped \
  yyhis-web:v1.0.0
```

## 🔧 高级配置

### 自定义Nginx配置

```bash
# 创建自定义nginx配置
cp docker/nginx.conf.template custom-nginx.conf
vim custom-nginx.conf

# 挂载自定义配置
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  -v $(pwd)/.env:/app/config/.env:ro \
  -v $(pwd)/custom-nginx.conf:/etc/nginx/conf.d/default.conf:ro \
  yyhis-web:latest
```

### 多实例部署

```bash
# 部署测试实例
docker run -d --name yyhis-web-test -p 8081:80 \
  -v $(pwd)/test.env:/app/config/.env:ro yyhis-web:latest

# 部署生产实例  
docker run -d --name yyhis-web-prod -p 8082:80 \
  -v $(pwd)/prod.env:/app/config/.env:ro yyhis-web:latest
```

### 资源限制

```bash
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  --memory=512m \
  --cpus=1.0 \
  -v $(pwd)/.env:/app/config/.env:ro \
  yyhis-web:latest
```

## 🔍 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看日志
   docker logs yyhis-web
   
   # 检查配置文件
   cat docker/config/.env
   ```

2. **API请求失败**
   ```bash
   # 检查后端服务是否可访问
   curl http://your-backend-server:6596/health
   
   # 检查nginx代理配置
   docker exec yyhis-web cat /etc/nginx/conf.d/default.conf
   ```

3. **配置不生效**
   ```bash
   # 重启容器
   docker restart yyhis-web
   
   # 检查环境变量
   docker exec yyhis-web env | grep VITE
   ```

### 调试命令

```bash
# 进入容器
docker exec -it yyhis-web sh

# 检查nginx状态
docker exec yyhis-web nginx -t

# 查看进程
docker exec yyhis-web ps aux

# 检查网络
docker exec yyhis-web netstat -tlnp
```

## 📊 监控和维护

### 健康检查

```bash
# 检查容器健康状态
docker inspect yyhis-web | grep Health -A 10

# 手动健康检查
curl http://localhost:8080/health
```

### 日志管理

```bash
# 查看实时日志
docker logs -f yyhis-web

# 查看最近100行日志
docker logs --tail 100 yyhis-web

# 配置日志轮转
docker run -d \
  --name yyhis-web \
  --log-driver json-file \
  --log-opt max-size=10m \
  --log-opt max-file=3 \
  -p 8080:80 \
  yyhis-web:latest
```

### 备份和恢复

```bash
# 备份配置文件
cp docker/config/.env docker/config/.env.backup.$(date +%Y%m%d)

# 备份容器
docker commit yyhis-web yyhis-web:backup-$(date +%Y%m%d)

# 恢复配置
cp docker/config/.env.backup.20240101 docker/config/.env
docker restart yyhis-web
```

## ⚠️ 安全注意事项

1. **配置文件安全**
   - 不要在配置文件中使用明文密码
   - 限制配置文件的访问权限
   - 定期更换密钥

2. **网络安全**
   - 使用HTTPS进行生产部署
   - 配置防火墙规则
   - 限制容器网络访问

3. **镜像安全**
   - 定期更新基础镜像
   - 扫描镜像漏洞
   - 使用最小权限原则

## 🆘 技术支持

如遇到问题，请按以下步骤排查：

1. 检查Docker版本兼容性
2. 验证配置文件格式
3. 确认网络连接正常
4. 查看容器日志信息
5. 检查资源使用情况

更多详细信息请参考 `docker/README.md` 文件。
