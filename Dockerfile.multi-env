# 多环境构建 Dockerfile for YYHIS Web
# 支持构建时指定环境，将配置编译到镜像中

# 构建参数
ARG BUILD_ENV=production
ARG NODE_VERSION=20.19.0

# 第一阶段：构建阶段
FROM node:${NODE_VERSION}-alpine AS builder

# 设置构建环境参数
ARG BUILD_ENV
ENV BUILD_ENV=${BUILD_ENV}

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production --silent

# 复制源代码
COPY . .

# 复制对应环境的配置文件
RUN if [ "$BUILD_ENV" = "development" ]; then \
        cp .env.development .env.production; \
    elif [ "$BUILD_ENV" = "test" ]; then \
        cp .env.test .env.production; \
    fi

# 显示当前使用的环境配置
RUN echo "🔧 构建环境: $BUILD_ENV" && \
    echo "📋 环境配置:" && \
    cat .env.production

# 构建应用
RUN npm run build:prod

# 第二阶段：运行阶段
FROM nginx:1.25-alpine

# 安装必要工具
RUN apk add --no-cache bash curl

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY docker/nginx.conf.template /etc/nginx/templates/default.conf.template

# 复制启动脚本（简化版，不需要运行时配置注入）
COPY docker/docker-entrypoint-simple.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# 设置默认环境变量
ENV NGINX_PORT=80
ENV API_PROXY_PASS=http://localhost:6596
ENV CLIENT_MAX_BODY_SIZE=10m

# 设置构建信息标签
ARG BUILD_ENV
LABEL build.env=${BUILD_ENV}
LABEL build.date=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
LABEL description="YYHIS Web - ${BUILD_ENV} environment"

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${NGINX_PORT}/ || exit 1

# 启动脚本
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
