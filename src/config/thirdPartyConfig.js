/**
 * 第三方系统集成配置
 */

// 获取运行时配置（优先）或编译时环境变量（备用）
const getRuntimeConfig = (key, defaultValue = '') => {
  // 优先从运行时配置获取
  if (typeof window !== 'undefined' && window.__APP_CONFIG__) {
    return window.__APP_CONFIG__[key] || defaultValue
  }
  // 备用：从编译时环境变量获取
  return import.meta.env[key] || defaultValue
}

// 迎春花质控系统配置 - 支持运行时动态配置
export const yingchunhuaConfig = {
  // 从运行时配置或环境变量获取配置，如果没有则使用默认值
  get sdkUrl() {
    return getRuntimeConfig('VITE_YINGCHUNHUA_SDK_URL', 'http://183.242.68.188:8094/client_app_iframe/index.js')
  },
  get appKey() {
    return getRuntimeConfig('VITE_YINGCHUNHUA_APP_KEY', '')
  },
  get appSecretKey() {
    return getRuntimeConfig('VITE_YINGCHUNHUA_APP_SECRET_KEY', '')
  },

  // 默认配置值
  linkType: '2', // 1-app, 2-web
  qualityTarget: '2' // 1-临床, 2-病理
}


// 获取当前环境配置
export const getCurrentConfig = (system = 'yingchunhua') => {
  const configs = {
    yingchunhua: yingchunhuaConfig
  }

  return configs[system] || yingchunhuaConfig
}

// 患者数据字段映射配置
export const patientDataMapping = {
  // 必填字段
  required: [
    'patient_id',
    'visit_sn', 
    'visit_type',
    'hospital_code',
    'hospital_name',
    'visit_doctor_no',
    'visit_doctor_name',
    'name',
    'gender',
    'date_of_birth',
    'occupation_code',
    'occupation_name'
  ],
  
  // 住院患者必填字段
  inpatientRequired: [
    'medical_record_no',
    'inpatient_no',
    'admission_datetime'
  ],
  
  // 门诊患者必填字段
  outpatientRequired: [
    'outpatient_no',
    'visit_datetime',
    'regis_sn',
    'regis_datetime',
    'regis_dept_code',
    'regis_dept_name'
  ],
  
  // 字段默认值
  defaults: {
    patient_gender: 'NULL',
    nationality: '中国',
    ethnicity: '汉族',
    newbron_mark: '否',
    visit_status: '否',
    patient_identity: '其他',
    blood_type_s: 'NULL',
    bolld_type_e: 'NULL',
    height: 'NULL',
    weight: 'NULL',
    certificate_type: '身份证',
    health_card_type: 'NULL',
    health_card_no: 'NULL',
    tsblbs: 'NULL',
    is_hospital_infected: 'NULL',
    extend_data1: 'NULL',
    extend_data2: 'NULL',
    record_status: '1',
    first_visit_mark: '是',
    regis_charge_price: '0.000',
    regis_paid_price: '0.000'
  }
}



// 质控目标映射
export const qualityTargetMapping = {
  '1': '临床',
  '2': '病理'
}

// 集成系统配置
export const integrationSystems = [
  {
    id: 'yingchunhua',
    name: '迎春花质控系统',
    description: '医疗质量控制系统',
    modules: ['过程管理', '质控结果', '质控日志', '费用管理', '质控小结']
  },
  {
    id: 'drg',
    name: 'DRG系统',
    description: '诊断相关分组系统',
    modules: ['费用管理', '病案分组']
  }
]

// 浏览器兼容性检查
export const browserCompatibility = {
  checkCompatibility() {
    const userAgent = navigator.userAgent
    let detectedBrowser = 'Unknown'
    let isSupported = false

    if (userAgent.includes('Chrome')) {
      detectedBrowser = 'Chrome'
      const version = parseInt(userAgent.match(/Chrome\/(\d+)/)?.[1] || '0')
      isSupported = version >= 49
    } else if (userAgent.includes('Firefox')) {
      detectedBrowser = 'Firefox'
      const version = parseInt(userAgent.match(/Firefox\/(\d+)/)?.[1] || '0')
      isSupported = version >= 45
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      detectedBrowser = 'Safari'
      isSupported = true // Safari通常支持现代特性
    } else if (userAgent.includes('Edge')) {
      detectedBrowser = 'Edge'
      isSupported = true
    } else if (userAgent.includes('Trident') || userAgent.includes('MSIE')) {
      detectedBrowser = 'Internet Explorer'
      const version = parseInt(userAgent.match(/(?:MSIE |rv:)(\d+)/)?.[1] || '0')
      isSupported = version >= 11
    }

    return {
      detectedBrowser,
      isSupported,
      userAgent
    }
  }
}

export default {
  getCurrentConfig,
  patientDataMapping,
  integrationSystems,
  browserCompatibility
}
