/**
 * 应用配置管理
 * 统一管理环境变量和应用配置
 */

// 获取运行时配置（优先）或编译时环境变量（备用）
const getRuntimeConfig = (key, defaultValue = '') => {
  // 优先从运行时配置获取
  if (typeof window !== 'undefined' && window.__APP_CONFIG__) {
    return window.__APP_CONFIG__[key] || defaultValue
  }
  // 备用：从编译时环境变量获取
  return import.meta.env[key] || defaultValue
}

// 获取环境变量（保持向后兼容）
const env = import.meta.env

/**
 * 应用基础配置 - 支持运行时动态配置
 */
export const appConfig = {
  // 应用信息 - 支持运行时配置
  get title() {
    return getRuntimeConfig('VITE_APP_TITLE', '测试医院管理系统')
  },
  get version() {
    return getRuntimeConfig('VITE_APP_VERSION', '1.0.0')
  },
  get env() {
    return getRuntimeConfig('VITE_APP_ENV', 'development')
  },

  // 开发模式判断
  isDev: env.DEV,
  isProd: env.PROD,
  get isTest() {
    return this.env === 'test'
  }
}

/**
 * API配置 - 支持运行时动态配置
 */
export const apiConfig = {
  // 基础URL - 支持运行时配置
  get baseURL() {
    const runtimeUrl = getRuntimeConfig('VITE_API_BASE_URL')
    if (runtimeUrl) return runtimeUrl
    return env.VITE_API_BASE_URL || (env.DEV ? '/api' : 'http://localhost:6596')
  },

  // 后端服务器地址 - 支持运行时配置
  get backendURL() {
    return getRuntimeConfig('VITE_BACKEND_URL', 'http://localhost:6596')
  },

  // 请求超时时间 - 支持运行时配置
  get timeout() {
    const runtimeTimeout = getRuntimeConfig('VITE_API_TIMEOUT')
    return parseInt(runtimeTimeout || env.VITE_API_TIMEOUT) || 10000
  },

  // API版本
  version: 'v1',

  // 请求头配置
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },

  // 是否使用代理
  get useProxy() {
    return env.DEV && this.baseURL === '/api'
  }
}

/**
 * 获取完整的API地址
 * @param {string} endpoint - API端点
 * @returns {string} 完整的API地址
 */
export const getApiUrl = (endpoint) => {
  // 移除端点开头的斜杠（如果有的话）
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint
  return `${apiConfig.baseURL}/${cleanEndpoint}`
}

/**
 * 日志配置
 */
export const logConfig = {
  // 是否启用日志
  enabled: appConfig.isDev,
  
  // 日志级别
  level: appConfig.isDev ? 'debug' : 'error'
}

// 导出默认配置
export default {
  app: appConfig,
  api: apiConfig,
  log: logConfig,
  getApiUrl
}
